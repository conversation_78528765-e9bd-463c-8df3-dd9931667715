<?php

namespace App\Service;

use Symfony\Contracts\Cache\CacheInterface;
use Symfony\Contracts\Cache\ItemInterface;

class CacheService
{
    public function __construct(
        private CacheInterface $cache
    ) {}

    /**
     * Get data from cache, or execute callback if not cached
     */
    public function get(string $key, callable $callback, int $ttl = 300): mixed
    {
        return $this->cache->get($key, function (ItemInterface $item) use ($callback, $ttl, $key) {
            // Set how long this should be cached (5 minutes = 300 seconds)
            $item->expiresAfter($ttl);
            
            // This will only run if the data is NOT in cache
            echo "🔥 CACHE MISS - Fetching from database for key: $key\n";
            return $callback();
        });
    }

    /**
     * Remove item from cache
     */
    public function delete(string $key): bool
    {
        return $this->cache->delete($key);
    }
}