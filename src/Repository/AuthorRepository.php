<?php

namespace App\Repository;

use App\Entity\Author;
use App\Service\CacheService;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Author>
 */
class AuthorRepository extends ServiceEntityRepository
{
    public function __construct(
        ManagerRegistry $registry,
        private CacheService $cache
        )
    {
        parent::__construct($registry, Author::class);
    }
    
     /**
     * Get all authors - WITH CACHE  
     */
    public function findAllCached(): array
    {
        $cacheKey = "authors_all";
        
        return $this->cache->get($cacheKey, function() {
            // This only runs if NOT in cache
            return $this->findAll();
        }, 300);
    }

    /**
     * Save author and clear related cache
     */
    public function saveCached(Author $author): void
    {
        // Save to database first
        $this->getEntityManager()->persist($author);
        $this->getEntityManager()->flush();
        
        // Clear cache so next request gets fresh data
        $this->cache->delete("authors_all");
        
        echo "✅ Author saved and cache cleared!\n";
    }

     /**
     * Find author by ID - WITH CACHE
     */
    public function findCached(int $id): ?Author
    {
        $cacheKey = "author_$id";
        
        return $this->cache->get($cacheKey, function() use ($id) {
            // This only runs if NOT in cache
            return $this->find($id);
        }, 300); // Cache for 5 minutes
    }

    //    /**
    //     * @return Author[] Returns an array of Author objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('a')
    //            ->andWhere('a.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('a.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?Author
    //    {
    //        return $this->createQueryBuilder('a')
    //            ->andWhere('a.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}