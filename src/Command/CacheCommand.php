<?php

namespace App\Command;

use App\Entity\Author;
use App\Entity\Note;
use App\Repository\AuthorRepository;
use App\Repository\NoteRepository;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:cache:demo',
    description: 'Simple demo to understand Redis caching',
)]
class CacheCommand extends Command
{
    public function __construct(
        private AuthorRepository $authorRepo,
        private NoteRepository $noteRepo
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        
        $io->title('🚀 Redis Cache Demo');
        
        // First, let's create some test data if it doesn't exist
        $this->createTestData($io);
        
        // Demo 1: Show cache miss vs cache hit
        $io->section('Demo 1: Cache Miss vs Cache Hit');
        
        $io->writeln('📥 First call - This will be a CACHE MISS (data fetched from database):');
        $startTime = microtime(true);
        $author = $this->authorRepo->findCached(1);
        $firstCallTime = microtime(true) - $startTime;
        $io->writeln("⏱️  Time: " . round($firstCallTime * 1000, 2) . "ms");
        
        $io->writeln('');
        $io->writeln('📥 Second call - This will be a CACHE HIT (data from Redis):');
        $startTime = microtime(true);
        $author = $this->authorRepo->findCached(1);
        $secondCallTime = microtime(true) - $startTime;
        $io->writeln("⏱️  Time: " . round($secondCallTime * 1000, 2) . "ms");
        
        $speedup = $firstCallTime / $secondCallTime;
        $io->success("🏃‍♂️ Cache was " . round($speedup, 1) . "x faster!");
        
        // Demo 2: Show cache invalidation
        $io->section('Demo 2: Cache Invalidation');
        
        $io->writeln('👀 Let\'s see what happens when we update data...');
        
        if ($author) {
            $author->setName($author->getName() . ' (Updated)');
            $this->authorRepo->saveCached($author);
            
            $io->writeln('📥 Now fetching again - should be CACHE MISS because we cleared cache:');
            $updatedAuthor = $this->authorRepo->findCached(1);
            $io->writeln("✅ Author name: " . $updatedAuthor->getName());
        }
        
        $io->note('💡 Pro tip: Open http://localhost:8081 to see your cached data in Redis Commander!');
        
        return Command::SUCCESS;
    }
    
    private function createTestData(SymfonyStyle $io): void
    {
        // Check if we have test data
        $author = $this->authorRepo->find(1);
        
        if (!$author) {
            $io->writeln('Creating test data...');
            
            $author = new Author();
            $author->setName('John Doe');
            $this->authorRepo->saveCached($author);
            
            
            $io->writeln('✅ Test data created!');
        }
    }
}