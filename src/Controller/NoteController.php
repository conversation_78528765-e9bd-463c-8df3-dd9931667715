<?php

namespace App\Controller;

use App\Entity\Author;
use App\Entity\Note;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

#[Route('')]
class NoteController extends AbstractController
{
    #[Route('', methods: ['GET'])]
    public function index(EntityManagerInterface $em): JsonResponse
    {
        $notes = $em->getRepository(Note::class)->findAll();

        $data = array_map(fn(Note $note) => [
            'id' => $note->getId(),
            'title' => $note->getTitle(),
            'content' => $note->getContent(),
            'createdAt' => $note->getCreatedAt()->format('Y-m-d H:i:s'),
        ], $notes);

        return $this->json($data);
    }

    #[Route('', methods: ['POST'])]
    public function create(Request $request, EntityManagerInterface $em): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        $note = new Note();
        $note->setTitle($data['title']);
        $note->setContent($data['content']);
        $note->setCreatedAt(new \DateTimeImmutable());

        $author = $em->getRepository(Author::class)->findOneBy(['name'=>$data['author']]);
        if (!$author) {
            $author = new Author();
            $author->setName($data['author']);
            $em->persist($author);
        }
        $note->setAuthor($author);

        $em->persist($note);
        $em->flush();

        return $this->json(['message' => 'Note created!', 'id' => $note->getId()]);
    }

    #[Route('/{id}', methods: ['GET'])]
    public function show(int $id, EntityManagerInterface $em): JsonResponse
    {
        $note = $em->getRepository(Note::class)->find($id);

        if (!$note) {
            return $this->json(['error' => 'Note not found'], 404);
        }

        return $this->json([
            'id' => $note->getId(),
            'title' => $note->getTitle(),
            'content' => $note->getContent(),
            'createdAt' => $note->getCreatedAt()->format('Y-m-d H:i:s'),
        ]);
    }

    #[Route('/{id}', methods: ['PUT'])]
    public function update(int $id, Request $request, EntityManagerInterface $em): JsonResponse
    {
        $note = $em->getRepository(Note::class)->find($id);

        if (!$note) {
            return $this->json(['error' => 'Note not found'], 404);
        }

        $data = json_decode($request->getContent(), true);

        if (isset($data['title'])) {
            $note->setTitle($data['title']);
        }
        if (isset($data['content'])) {
            $note->setContent($data['content']);
        }

        $em->flush();

        return $this->json(['message' => 'Note updated!']);
    }

    #[Route('/{id}', methods: ['DELETE'])]
    public function delete(int $id, EntityManagerInterface $em): JsonResponse
    {
        $note = $em->getRepository(Note::class)->find($id);

        if (!$note) {
            return $this->json(['error' => 'Note not found'], 404);
        }

        $em->remove($note);
        $em->flush();

        return $this->json(['message' => 'Note deleted!']);
    }
}