<?php

namespace App\Controller;

use App\Entity\Author;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\SerializerInterface;

#[Route('/author', name: 'author')]
final class AuthorController extends AbstractController
{
    #[Route('/list', name:'list')]
    public function list(Request $request, EntityManagerInterface $em, SerializerInterface $serializer): JsonResponse
    {
        $authors = $em->getRepository(Author::class)->findAll();
        $authors = $serializer->serialize($authors, 'json',[
            'circular_reference_handler'=> function($object) {
                return $object->getId();
            }
        ]);
        return $this->json($authors);
    }

    #[Route('/create', name:'create',methods:['POST'])]
    public function create(Request $request, EntityManagerInterface $em): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $author = new Author();
        $author->setName($data['name']);
        $em->persist($author);
        $em->flush();
        return $this->json($author);
    }
    

}