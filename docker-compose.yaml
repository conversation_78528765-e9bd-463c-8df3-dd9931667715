services:
  # 🔹 PostgreSQL Database
  postgres_db:
    image: postgres:15
    container_name: postgres_db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgresdb
    ports:
      - 5432:5432
    volumes:
      - "../_fixtures/.pgdata:/var/lib/postgresql/data"
    restart: always

  # 🔹 pgAdmin (GUI for PostgreSQL)
  pgadmin:
    image: dpage/pgadmin4
    container_name: pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - 8083:80
    restart: always

  redis:
    image: redis:7-alpine
    container_name: redis_cache
    ports:
      - 6379:6379
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: always

  # 🔹 Redis Commander (GUI for Redis - Optional)
  redis_commander:
    image: rediscommander/redis-commander:latest
    container_name: redis_commander
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - 8084:8081
    depends_on:
      - redis
    restart: always

  ###> doctrine/doctrine-bundle ###
  database:
    image: postgres:${POSTGRES_VERSION:-16}-alpine
    environment:
      POSTGRES_DB: postgresdb
      # You should definitely change the password in production
      POSTGRES_PASSWORD: postgres
      POSTGRES_USER: postgres
    volumes:
      - database_data:/var/lib/postgresql/data:rw
      # You may use a bind-mounted host directory instead, so that it is harder to accidentally remove the volume and lose all your data!
      # - ./docker/db/data:/var/lib/postgresql/data:rw
###< doctrine/doctrine-bundle ###

volumes:
  ###> doctrine/doctrine-bundle ###
  database_data:
  redis_data:
###< doctrine/doctrine-bundle ###
